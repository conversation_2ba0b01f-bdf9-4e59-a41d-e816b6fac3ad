# we.zhongcaiguoke.com 域名配置
server {
	listen 80;
        server_name we.zhongcaiguoke.com;

    # 将HTTP请求重定向到HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name we.zhongcaiguoke.com;

    # SSL证书配置
    ssl_certificate /usr/local/nginx/conf/cert/we.zhongcaiguoke.com_certificate.pem;
    ssl_certificate_key /usr/local/nginx/conf/cert/we.zhongcaiguoke.com_private.key;

    # SSL参数优化
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers EECDH+CHACHA20:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    access_log /data/wwwlogs/nginx_1.log combined;
    root /data/wwwroot/zcgk;
    index index.html index.htm index.php;
    #error_page 404 /404.html;
    #error_page 502 /502.html;

    location /nginx_status {
      stub_status on;
      access_log off;
      allow 127.0.0.1;
      deny all;
    }

    location / {
        client_max_body_size 10m;
        if (!-e $request_filename){
            rewrite ^(.*)$ /index.php?s=$1 last;
         }
    }

    location ~ [^/]\.php(/|$) {
        #fastcgi_pass remote_php_ip:9000;
      fastcgi_pass unix:/dev/shm/php-cgi.sock;
      fastcgi_index index.php;
      include fastcgi.conf;

    }


    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf|flv|mp4|ico)$ {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

        expires 30d;

      access_log off;
    }

    location ~ .*\.(js|css)?$ {
      expires 30d;
      access_log off;
    }
    location ~ ^/(\.user.ini|\.ht|\.git|\.svn|\.project|LICENSE|README.md) {
      deny all;
    }
}

# station.zhongcaiguoke.com 域名配置
server {
	listen 80;
        server_name station.zhongcaiguoke.com;

    # 将HTTP请求重定向到HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name station.zhongcaiguoke.com;

    # SSL证书配置
    ssl_certificate /usr/local/nginx/conf/cert/station.zhongcaiguoke.com_certificate.pem;
    ssl_certificate_key /usr/local/nginx/conf/cert/station.zhongcaiguoke.com_private.key;
    
    # SSL参数优化
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers EECDH+CHACHA20:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    access_log /data/wwwlogs/nginx_1.log combined;
    root /data/wwwroot/zcgk;
    index index.html index.htm index.php;
    #error_page 404 /404.html;
    #error_page 502 /502.html;

    location /nginx_status {
      stub_status on;
      access_log off;
      allow 127.0.0.1;
      deny all;
    }

    location / {
        client_max_body_size 10m;
        if (!-e $request_filename){
            rewrite ^(.*)$ /index.php?s=$1 last;
         }
    }

    location ~ [^/]\.php(/|$) {
        #fastcgi_pass remote_php_ip:9000;
      fastcgi_pass unix:/dev/shm/php-cgi.sock;
      fastcgi_index index.php;
      include fastcgi.conf;

    }


    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf|flv|mp4|ico)$ {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

        expires 30d;

      access_log off;
    }

    location ~ .*\.(js|css)?$ {
      expires 30d;
      access_log off;
    }
    location ~ ^/(\.user.ini|\.ht|\.git|\.svn|\.project|LICENSE|README.md) {
      deny all;
    }
}

# .cn 域名配置
server {
	listen 80;
        server_name station.zhongcaiguoke.cn c.zhongcaiguoke.cn;

    # 将HTTP请求重定向到HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name station.zhongcaiguoke.cn c.zhongcaiguoke.cn;

    # SSL证书配置
    ssl_certificate /usr/local/nginx/conf/cert/zhongcaiguoke.cn_certificate.pem;
    ssl_certificate_key /usr/local/nginx/conf/cert/zhongcaiguoke.cn_private.key;

    # SSL参数优化
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers EECDH+CHACHA20:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    access_log /data/wwwlogs/nginx_1.log combined;
    root /data/wwwroot/zcgk;
    index index.html index.htm index.php;
    #error_page 404 /404.html;
    #error_page 502 /502.html;

    location /nginx_status {
      stub_status on;
      access_log off;
      allow 127.0.0.1;
      deny all;
    }

    location / {
        client_max_body_size 10m;
        if (!-e $request_filename){
            rewrite ^(.*)$ /index.php?s=$1 last;
         }
    }

    location ~ [^/]\.php(/|$) {
        #fastcgi_pass remote_php_ip:9000;
      fastcgi_pass unix:/dev/shm/php-cgi.sock;
      fastcgi_index index.php;
      include fastcgi.conf;

    }


    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf|flv|mp4|ico)$ {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

        expires 30d;

      access_log off;
    }

    location ~ .*\.(js|css)?$ {
      expires 30d;
      access_log off;
    }
    location ~ ^/(\.user.ini|\.ht|\.git|\.svn|\.project|LICENSE|README.md) {
      deny all;
    }
}
