<?php
$configs = array(
	'ADMIN_AUTH_KEY'    => 'admin',
	'USER_AUTH_ON'      => '1',
	'USER_AUTH_TYPE'    => '1',//2为即时验证模式，别的数字为登陆验证
	'RBAC_ROLE_TABLE'   => 'think_role',
	'RBAC_USER_TABLE'   => 'think_role_user',
	'RBAC_ACCESS_TABLE' => 'think_access',
	'RBAC_NODE_TABLE'   => 'think_node',
    'SHOW_PAGE_TRACE' => false, // 显示页面Trace信息
    "LOAD_EXT_FILE"=>"extend",
    'DEFAULT_MODULE'        =>  'Home',  // 默认模块
    'MODULE_ALLOW_LIST' => ['Prime','Other', 'Pub', 'Cli', 'Shop', 'Home', 'Int', 'Ofile', 'Station', 'Pwapi'],
    'URL_HTML_SUFFIX'       =>  '',  // URL伪静态后缀设置
    'URL_MODEL'             =>  2,       // URL访问模式,可选参数0、1、2、3,代表以下四种模式：
    // 0 (普通模式); 1 (PATHINFO 模式); 2 (REWRITE  模式); 3 (兼容模式)  默认为PATHINFO 模式
    'DEFAULT_FILTER'        =>  'htmlspecialchars,trim', // 默认参数过滤方法 用于I函数...
    'LOG_RECORD' => false,  // 关闭日志记录
    'SHOW_ERROR_MSG' => false,  // 关闭错误信息显示
    'LOCAL_DEBUG' => true,
    'VAR_PAGE'              =>"p", //分页配置

    'THUMB_SIZE' => [     //缩略图尺寸配置
        ['width' => 300, 'height' => 300],
        ['width' => 100, 'height' => 100],
    ],
    'APP_SUB_DOMAIN_DEPLOY'   =>    1, // 开启子域名配置
    'APP_SUB_DOMAIN_RULES' => [
        //'station.zhongcaiguoke.com' => 'Station',
        'station.zhongcaiguoke.cn' => 'Station',
        'c.zhongcaiguoke.com' => 'Other',
    ],

    // URL路由规则
    'URL_ROUTER_ON'   => true,   // 开启路由
    'URL_ROUTE_RULES' => array(
        // 排除静态文件路径，不进行路由解析
        '/^data\/.*\.(docx|doc|pdf|xls|xlsx)$/i' => false,
        'zsbindex' => 'Station/Index/zsbindex',
    ),

);

$db = include 'db.php';

return array_merge($configs, $db);

